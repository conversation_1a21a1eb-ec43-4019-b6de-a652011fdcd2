import router from '@adonisjs/core/services/router';
import { middleware } from '#start/kernel';
import TenanciesController from '#controllers/tenancies_controller';

export function tenancyRoutes() {
   router
      .group(() => {
         router.post('/', [TenanciesController, 'create']);
         router.get('/landlord/:landlordId', [TenanciesController, 'getAllTenanciesByLandlord']);
         router.get('/tenant/:tenantId', [TenanciesController, 'getAllTenanciesByTenant']);
         router.get('/:id', [TenanciesController, 'single']);
         router.put('/:id/status', [TenanciesController, 'updateStatus']);
         router.delete('/:id', [TenanciesController, 'delete']);

         // Add the new route for uploading signed agreement
         router.post('/:id/upload-agreement', [TenanciesController, 'uploadSignedAgreement']);
      })
      .prefix('/tenancy')
      .use(middleware.auth());
}
