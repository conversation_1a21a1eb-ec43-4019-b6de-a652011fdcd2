import type { DateTime } from 'luxon';
import { BaseModel, belongsTo, column, computed } from '@adonisjs/lucid/orm';
import type { BelongsTo } from '@adonisjs/lucid/types/relations';
import { R2ImageResource } from '../resources/r2_image_resource.js';
import User from '#models/user';
import Property from '#models/property';

export default class Tenancy extends BaseModel {
   @column({ isPrimary: true })
   declare id: number;

   @column()
   declare tenantId: number;

   @column()
   declare landlordId: number;

   @column()
   declare propertyId: number;

   @column()
   declare visitId: number;

   @column()
   declare tenantName: string;

   @column()
   declare tenantAddress: string;

   @column()
   declare tenantNic: string;

   @column()
   declare tenantPhone: string;

   @column()
   declare landlordName: string;

   @column()
   declare landlordAddress: string;

   @column()
   declare landlordNic: string;

   @column()
   declare landlordPhone: string;

   @column()
   declare typeOfProperty: string;

   @column()
   declare propertyAddress: string;

   @column.dateTime()
   declare tenancyStart: DateTime;

   @column()
   declare tenancyDuration: number;

   @column()
   declare securityDepositAmount: number;

   @column()
   declare rentAmount: number;

   @column()
   declare rentDueDay: number;

   @column()
   declare bankName: string;

   @column()
   declare bankBranch: string;

   @column()
   declare bankAccountName: string;

   @column()
   declare bankAccountNumber: string;

   @column.dateTime()
   declare agreementDate: DateTime;

   @column.dateTime({ autoCreate: true })
   declare createdAt: DateTime;

   @column.dateTime({ autoCreate: true, autoUpdate: true })
   declare updatedAt: DateTime;

   @column()
   declare status: 'ACTIVE' | 'COMPLETED' | 'EARLY_TERMINATED' | 'PENDING' | 'RENEWED' | 'REJECTED';

   @column()
   declare terminationReason?: string;

   @column.dateTime()
   declare terminationDate?: DateTime;

   @column.dateTime()
   declare actualEndDate?: DateTime;

   @column()
   declare signedAgreementUrlKey: string | null;

   @computed()
   get signedAgreementUrl() {
      return this.signedAgreementUrlKey
         ? R2ImageResource.getFullUrl(this.signedAgreementUrlKey)
         : null;
   }

   /* ------------------------------------------------------------------------------------------- */

   @computed()
   get tenancyEnd() {
      return this.tenancyStart.plus({ months: this.tenancyDuration });
   }

   /* ------------------------------------------------------------------------------------------- */

   @belongsTo(() => User, {
      foreignKey: 'tenantId',
   })
   declare tenant: BelongsTo<typeof User>;

   @belongsTo(() => User, {
      foreignKey: 'landlordId',
   })
   declare landlord: BelongsTo<typeof User>;

   @belongsTo(() => Property, {
      foreignKey: 'propertyId',
   })
   declare property: BelongsTo<typeof Property>;
}
