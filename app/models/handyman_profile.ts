import type { DateTime } from 'luxon';
import { BaseModel, belongsTo, column, computed, manyToMany } from '@adonisjs/lucid/orm';
import type { BelongsTo, ManyToMany } from '@adonisjs/lucid/types/relations';
import { R2ImageResource } from '../resources/r2_image_resource.js';
import HandymanSkill from '#models/handyman_skill';
import User from '#models/user';
import City from '#models/city';

export default class HandymanProfile extends BaseModel {
   @column({ isPrimary: true })
   declare id: number;

   @column()
   declare userId: number;

   @column()
   declare expYears: number;

   @column()
   declare adminApproved: boolean;

   @column()
   declare nic: string;

   @column()
   declare nicFrontImageUrlKey: string;

   @column()
   declare nicBackImageUrlKey: string;

   @column()
   declare certificate1ImageUrlKey: string;

   @column()
   declare certificate2ImageUrlKey: string;

   @column()
   declare certificate3ImageUrlKey: string;

   /* ------------------------------------------------------------------------------------------- */

   @computed()
   get nicFrontImageUrl() {
      return R2ImageResource.getFullUrl(this.nicFrontImageUrlKey);
   }

   @computed()
   get nicBackImageUrl() {
      return R2ImageResource.getFullUrl(this.nicBackImageUrlKey);
   }

   @computed()
   get certificate1ImageUrl() {
      return R2ImageResource.getFullUrl(this.certificate1ImageUrlKey);
   }

   @computed()
   get certificate2ImageUrl() {
      return R2ImageResource.getFullUrl(this.certificate2ImageUrlKey);
   }

   @computed()
   get certificate3ImageUrl() {
      return R2ImageResource.getFullUrl(this.certificate3ImageUrlKey);
   }

   /* ------------------------------------------------------------------------------------------- */

   @column.dateTime({ autoCreate: true })
   declare createdAt: DateTime;

   @column.dateTime({ autoCreate: true, autoUpdate: true })
   declare updatedAt: DateTime;

   @column()
   declare hourlyRate: number;

   /* ------------------------------------------------------------------------------------------- */

   @belongsTo(() => User)
   declare user: BelongsTo<typeof User>;

   @manyToMany(() => HandymanSkill, {
      localKey: 'id',
      relatedKey: 'id',
      pivotForeignKey: 'handyman_id',
      pivotRelatedForeignKey: 'handyman_skill_id',
      pivotTable: 'handymen_handyman_skills',
   })
   declare handymanSkills: ManyToMany<typeof HandymanSkill>;

   @manyToMany(() => City, {
      localKey: 'id',
      relatedKey: 'id',
      pivotForeignKey: 'handyman_id',
      pivotRelatedForeignKey: 'city_id',
      pivotTable: 'handymen_cities',
   })
   declare cities: ManyToMany<typeof City>;
}
