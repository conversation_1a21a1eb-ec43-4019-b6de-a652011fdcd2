import type { DateTime } from 'luxon';
import { BaseModel, belongsTo, column, computed } from '@adonisjs/lucid/orm';
import type { BelongsTo } from '@adonisjs/lucid/types/relations';
import { R2ImageResource } from '../resources/r2_image_resource.js';
import Tenancy from '#models/tenancy';
import HandymanProfile from '#models/handyman_profile';
import User from '#models/user';

export default class TenancyMaintenance extends BaseModel {
   // add the table name here
   static table = 'tenancy_maintenance';

   @column({ isPrimary: true })
   declare id: number;

   @column()
   declare tenancyId: number;

   @column()
   declare handymanId: number | null;

   @column()
   declare handymanProfileId: number | null;

   @column()
   declare title: string;

   @column()
   declare description: string;

   @column()
   declare issueImage1: string | null;

   @column()
   declare issueImage2: string | null;

   @column()
   declare issueImage3: string | null;

   @computed()
   get issueImage1Url() {
      return this.issueImage1 ? R2ImageResource.getFullUrl(this.issueImage1) : null;
   }

   @computed()
   get issueImage2Url() {
      return this.issueImage2 ? R2ImageResource.getFullUrl(this.issueImage2) : null;
   }

   @computed()
   get issueImage3Url() {
      return this.issueImage3 ? R2ImageResource.getFullUrl(this.issueImage3) : null;
   }

   @column()
   declare completionImage1: string | null;

   @column()
   declare completionImage2: string | null;

   @column()
   declare completionImage3: string | null;

   @computed()
   get completionImage1Url() {
      return this.completionImage1 ? R2ImageResource.getFullUrl(this.completionImage1) : null;
   }

   @computed()
   get completionImage2Url() {
      return this.completionImage2 ? R2ImageResource.getFullUrl(this.completionImage2) : null;
   }

   @computed()
   get completionImage3Url() {
      return this.completionImage3 ? R2ImageResource.getFullUrl(this.completionImage3) : null;
   }

   @column()
   declare priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';

   @column()
   declare status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'EXPIRED';

   @column.dateTime()
   declare preferredDateTime: DateTime | null;

   @column.dateTime()
   declare scheduledDateTime: DateTime | null;

   @column.dateTime()
   declare completionDateTime: DateTime | null;

   @column({
      consume: value => Number(value),
   })
   declare estimatedCost: number | null;

   @column({
      consume: value => Number(value),
   })
   declare finalCost: number | null;

   @column()
   declare landlordWillPay: boolean;

   @column()
   declare rating: number | null;

   @column()
   declare feedback: string | null;

   @column()
   declare landlordNotes: string | null;

   @column()
   declare handymanNotes: string | null;

   @column.dateTime({ autoCreate: true })
   declare createdAt: DateTime;

   @column.dateTime({ autoCreate: true, autoUpdate: true })
   declare updatedAt: DateTime;

   @column()
   declare handymanStatus: 'PENDING' | 'ACCEPTED' | 'REJECTED' | 'STARTED' | 'COMPLETED' | 'EXPIRED' | null;

   /* ------------------------------------------------------------------------------------------- */

   @belongsTo(() => Tenancy)
   declare tenancy: BelongsTo<typeof Tenancy>;

   @belongsTo(() => User, {
      localKey: 'id',
      foreignKey: 'handymanId',
   })
   declare handyman: BelongsTo<typeof User>;

   @belongsTo(() => HandymanProfile)
   declare handymanProfile: BelongsTo<typeof HandymanProfile>;
}
