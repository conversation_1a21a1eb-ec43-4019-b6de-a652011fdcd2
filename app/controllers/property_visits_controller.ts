import type { HttpContext } from '@adonisjs/core/http';
import { DateTime } from 'luxon';
import PropertyVisitsResource from '../resources/property_visits_resource.js';
import { UserResource } from '../resources/user_resource.js';
import { SmsGatewayResource } from '../resources/sms_gateway_resource.js';
import {
   validatorCreatePropertyVisit,
   vineGetVisitsQuery,
   vineUpdateVisitArchiveStatus,
   vineUpdateVisitStatus,
   vineUpdateVisitTime,
} from '#validators/property_visit';
import PropertyVisit from '#models/property_visit';
import PropertyListing from '#models/property_listing';

import UserNotAllowedException from '#exceptions/user/user_not_allowed_exception';
import NotFoundException from '#exceptions/not_found_exception';
import AlreadyExistException from '#exceptions/already_exist_exception';

/* ---------------------------------------------------------------------------------------------- */

export default class PropertyVisitsController {
   /**
    * Creates a new visit for a given listing for auth user
    */
   async create({ request, response, auth }: HttpContext) {
      const user = auth.getUserOrFail();

      await user.load('profile');

      /*
       * Check if the user is a tenant
       */
      if (!UserResource.onlyTenant(user))
         throw new UserNotAllowedException();

      const payload = await request.validateUsing(validatorCreatePropertyVisit);

      /*
       * Check if a visit already exist for the user
       */
      const existingVisit = await PropertyVisitsResource.getExistingVisitsForListingByTenant(
         payload.listingId,
         user.id,
      );

      if (existingVisit.length > 0) {
         throw new AlreadyExistException('Visit already exist for the user');
      }

      const otherVisit = await PropertyVisit.query().where('listingId', payload.listingId).first();

      if (otherVisit) {
         /*
          * Check if another visit exists within the time range
          */
         const otherVisitsWithinTime = await PropertyVisitsResource.getListingVisitsByDateTime(
            otherVisit.listingId,
            payload.visitDateTime,
            'APPROVED',
         );

         if (otherVisitsWithinTime.length > 0) {
            throw new AlreadyExistException('Timeslot not available, Please try after or before 20 mints');
         }
      }

      /*
       * Get the listing details
       */
      const listing = await PropertyListing.query()
         .where('id', payload.listingId)
         .preload('landlord', (landlord) => {
            landlord.preload('profile');
         })
         .firstOrFail();

      if (!listing) {
         throw new NotFoundException(`Listing not found: ${payload.listingId}`);
      }

      const visitDateTime = DateTime.fromJSDate(payload.visitDateTime);

      const visit = await PropertyVisit.create({
         listingId: payload.listingId,
         tenantId: user.id,
         landlordId: listing.landlordId,
         visitDateTime,
         visitRemarks: payload.visitRemarks,
         status: 'PENDING',
      });
      // send SMS to landlord

      await SmsGatewayResource.sendMessage(listing.landlord.profile.phone1, `You have new visit request from ${user.profile?.fullName} for your property " ${listing.title}" . Please login to your account to view the details`);

      // send SMS to Tenant
      await SmsGatewayResource.sendMessage(user.profile.phone1, `Thank you for choosing Tenantrix. Your visit request for "${listing.title}" has been submitted. Please wait for landlord to approve the request.`);

      return response.ok(visit);
   }

   /* ------------------------------------------------------------------------------------------- */

   /**
    * Updates the visit date and time, also set the status as approved
    */
   async updateDateTime({ request, response, auth }: HttpContext) {
      const user = auth.getUserOrFail();

      if (!UserResource.onlyLandlord(user))
         throw new UserNotAllowedException();

      const payload = await request.validateUsing(vineUpdateVisitTime);

      // debug

      const visit = await PropertyVisit.find(payload.visitId);
      if (!visit)
         throw new NotFoundException('Visit not found');

      const existingVisits = await PropertyVisitsResource.getListingVisitsByDateTime(
         visit.listingId,
         payload.visitDateTime,
      );

      if (existingVisits.length > 0) {
         // return errorVisitTimeSlotNotAvailable(response, existingVisits);
         throw new AlreadyExistException('Timeslot not available');
      }

      visit.visitDateTime = DateTime.fromJSDate(payload.visitDateTime);
      visit.status = 'APPROVED';

      await visit.save();

      return response.ok(visit);
   }

   /* ------------------------------------------------------------------------------------------- */

   /**
    * Get the listing visit detail for the current user
    */
   async getVisitByListing({ request, response, auth }: HttpContext) {
      const user = auth.user;

      if (!user) {
         return response.badRequest();
      }

      const listingId = request.param('listingId');

      const visit = await PropertyVisit.query()
         .where('tenantId', user.id)
         .andWhere('listingId', listingId)
         .first();

      return response.ok(visit);
   }

   /* ------------------------------------------------------------------------------------------- */

   /**
    * Get all visits for the user
    */
   async getAllVisitsForUser({ request, response, auth }: HttpContext) {
      const user = auth.user;

      if (!user) {
         return response.badRequest();
      }

      /**
       * Query parameters
       */
      const page = request.input('page', 1);
      const perPage = request.input('perPage', 15);

      const validatedQueries = await vineGetVisitsQuery.validate(request.all());

      const visits = await PropertyVisitsResource.GetVisitsForUser(
         user.id,
         'tenant',
         validatedQueries,
         page,
         perPage,
      );

      return response.ok(visits);
   }

   /* ------------------------------------------------------------------------------------------- */

   async getAllVisitsForLandlord({ request, response }: HttpContext) {
      const landlordId = request.param('landlordId');

      const page = request.input('page', 1);
      const perPage = request.input('perPage', 15);

      const validatedQueries = await vineGetVisitsQuery.validate(request.all());

      const visits = await PropertyVisitsResource.GetVisitsForUser(
         landlordId,
         'landlord',
         validatedQueries,
         page,
         perPage,
      );

      return response.ok(visits);
   }

   /* ------------------------------------------------------------------------------------------- */

   /**
    * Get a single visit details
    */
   async single({ request, response }: HttpContext) {
      const id = request.param('visitId');

      const visit = await PropertyVisit.query()
         .where('id', id)
         .preload('tenant', (tenant) => {
            tenant.preload('profile');
         })
         .preload('propertyListing', (listing) => {
            listing.preload('property', (property) => {
               property.preload('propertyImages');
               property.preload('city');
               property.preload('propertyType');
               property.preload('propertyFeatures');
               property.preload('user', (user) => {
                  user.preload('profile');
               });
            });
         })
         .first();

      if (!visit) {
         throw new NotFoundException('Visit not found');
      }

      const now = DateTime.local().setZone('Asia/Colombo');
      if (visit.status === 'PENDING' || visit.status === 'APPROVED') {
         if (visit.visitDateTime < now) {
            visit.status = 'EXPIRED';
            await visit.save();
         }
      }

      return response.ok(visit);
   }

   /* ------------------------------------------------------------------------------------------- */

   /**
    * Update the visit status
    */
   async updateVisitStatus({ request, response }: HttpContext) {
      const payload = await request.validateUsing(vineUpdateVisitStatus);

      const listing = await PropertyVisit.find(payload.visitId);

      if (!listing) {
         return response.badRequest({ message: 'Invalid visit' });
      }

      listing.status = payload.status;
      listing.archived = false;
      await listing.save();

      return response.ok(listing);
   }

   /* ------------------------------------------------------------------------------------------- */

   /**
    * Set visit as archived
    */
   async setArchivedState({ request, response }: HttpContext) {
      const payload = await request.validateUsing(vineUpdateVisitArchiveStatus);

      if (!payload.visitId) {
         return response.badRequest({ message: 'Visit id is required' });
      }

      const visit = await PropertyVisit.find(payload.visitId);
      if (!visit) {
         return response.notFound({ message: 'Invalid visit' });
      }

      visit.archived = payload.archived;

      await visit.save();

      return response.ok(visit);
   }

   /* ------------------------------------------------------------------------------------------- */
}
