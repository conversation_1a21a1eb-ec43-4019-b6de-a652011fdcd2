import type { HttpContext } from '@adonisjs/core/http';
import { DateTime } from 'luxon';
import { cuid } from '@adonisjs/core/helpers';
import { SmsGatewayResource } from '../resources/sms_gateway_resource.js';
import TenancyMaintenance from '#models/tenancy_maintenance';
import Tenancy from '#models/tenancy';
import HandymanProfile from '#models/handyman_profile';
import {
   vineTenancyMaintenanceCreate,
   vineTenancyMaintenanceHandymanAction,
   vineTenancyMaintenanceLandlordAction,
   vineTenancyMaintenanceLandlordNotes,
   vineTenancyMaintenanceStatusUpdate,
   vineTenancyMaintenanceUpdate,
} from '#validators/tenancy_maintenance';

/* ---------------------------------------------------------------------------------------------- */

export default class TenancyMaintenanceController {
   /**
    * Create a new maintenance request
    */
   async create({ request, response, auth }: HttpContext) {
      const user = auth.user!;
      const payload = await request.validateUsing(vineTenancyMaintenanceCreate);

      try {
         // Verify the tenancy exists and user is associated with it
         const tenancy = await Tenancy.find(payload.tenancyId);

         // check if tenancy exists
         if (!tenancy) {
            return response.notFound({
               message: 'Tenancy not found',
            });
         }

         // Check if user is either the tenant or landlord of this tenancy
         if (tenancy.tenantId !== user.id && tenancy.landlordId !== user.id) {
            return response.forbidden({
               message: 'You are not authorized to create maintenance requests for this tenancy',
            });
         }

         // Create the maintenance request
         const maintenance = await TenancyMaintenance.create({
            tenancyId: payload.tenancyId,
            title: payload.title,
            description: payload.description,
            priority: payload.priority,
            preferredDateTime: payload.preferredDateTime
               ? DateTime.fromISO(payload.preferredDateTime)
               : null,
            landlordWillPay: payload.landlordWillPay ?? true,
            status: 'PENDING',
            handymanId: null,
            scheduledDateTime: null,
            completionDateTime: null,
            estimatedCost: null,
            finalCost: null,
            rating: null,
            feedback: null,
            landlordNotes: null,
            handymanNotes: null,
            issueImage1: null,
            issueImage2: null,
            issueImage3: null,
            completionImage1: null,
            completionImage2: null,
            completionImage3: null,
         });

         // Load relationships
         await maintenance.load('tenancy');
         if (maintenance.handymanId) {
            await maintenance.load('handyman');
         }

         return response.created(maintenance);
      }
      catch (error) {
         console.error(error);
         return response.internalServerError({
            message: 'Failed to create maintenance request',
         });
      }
   }

   /* ------------------------------------------------------------------------------------------- */

   /**
    * Update maintenance request
    */
   async update({ request, response, auth }: HttpContext) {
      const user = auth.user!;
      const id = request.param('id');
      const payload = await request.validateUsing(vineTenancyMaintenanceUpdate);

      try {
         const maintenance = await TenancyMaintenance.findOrFail(id);
         await maintenance.load('tenancy');

         // Verify user has permission to update
         if (
            maintenance.tenancy.tenantId !== user.id
            && maintenance.tenancy.landlordId !== user.id
            && maintenance.handymanId !== user.id
         ) {
            return response.forbidden({
               message: 'You are not authorized to update this maintenance request',
            });
         }

         // Validate status transitions
         if (payload.status) {
            const isValidTransition = this.validateStatusTransition(
               maintenance.status,
               payload.status,
               user.id === maintenance.tenancy.landlordId,
               user.id === maintenance.handymanId,
            );

            if (!isValidTransition) {
               return response.badRequest({
                  message: 'Invalid status transition',
               });
            }
         }

         // Update the maintenance request
         maintenance.merge({
            title: payload.title,
            description: payload.description,
            priority: payload.priority,
            status: payload.status,
            handymanId: payload.handymanId,
            scheduledDateTime: payload.scheduledDateTime
               ? DateTime.fromISO(payload.scheduledDateTime)
               : maintenance.scheduledDateTime,
            completionDateTime:
               payload.status === 'COMPLETED' ? DateTime.now() : maintenance.completionDateTime,
            estimatedCost: payload.estimatedCost,
            finalCost: payload.finalCost,
            landlordWillPay: payload.landlordWillPay,
            rating: payload.rating,
            feedback: payload.feedback,
            landlordNotes: payload.landlordNotes,
            handymanNotes: payload.handymanNotes,
            issueImage1: payload.issueImage1 ?? maintenance.issueImage1,
            issueImage2: payload.issueImage2 ?? maintenance.issueImage2,
            issueImage3: payload.issueImage3 ?? maintenance.issueImage3,
            completionImage1: payload.completionImage1 ?? maintenance.completionImage1,
            completionImage2: payload.completionImage2 ?? maintenance.completionImage2,
            completionImage3: payload.completionImage3 ?? maintenance.completionImage3,
         });

         await maintenance.save();

         // Reload relationships
         await maintenance.load('tenancy');
         if (maintenance.handymanId) {
            await maintenance.load('handyman');
         }

         return response.ok(maintenance);
      }
      catch (error) {
         console.error(error);
         return response.internalServerError({
            message: 'Failed to update maintenance request',
         });
      }
   }

   /* ------------------------------------------------------------------------------------------- */

   /**
    * Delete maintenance request
    */
   async delete({ request, response, auth }: HttpContext) {
      const user = auth.user!;
      const id = request.param('id');

      try {
         const maintenance = await TenancyMaintenance.findOrFail(id);
         await maintenance.load('tenancy');

         // Only allow deletion if status is PENDING and by the creator or landlord
         if (maintenance.status !== 'PENDING') {
            return response.badRequest({
               message: 'Only pending maintenance requests can be deleted',
            });
         }

         if (
            maintenance.tenancy.tenantId !== user.id
            && maintenance.tenancy.landlordId !== user.id
         ) {
            return response.forbidden({
               message: 'You are not authorized to delete this maintenance request',
            });
         }

         await maintenance.delete();

         return response.ok({
            message: 'Maintenance request deleted successfully',
         });
      }
      catch (error) {
         console.error(error);
         return response.internalServerError({
            message: 'Failed to delete maintenance request',
         });
      }
   }

   /* ------------------------------------------------------------------------------------------- */

   /**
    * Validate status transitions based on user role and current status
    */
   private validateStatusTransition(
      currentStatus: string,
      newStatus: string,
      isLandlord: boolean,
      isHandyman: boolean,
   ): boolean {
      // Define allowed transitions based on roles
      const allowedTransitions: Record<string, string[]> = {
         PENDING: ['APPROVED', 'REJECTED', 'CANCELLED'],
         APPROVED: ['IN_PROGRESS', 'CANCELLED'],
         IN_PROGRESS: ['COMPLETED', 'CANCELLED'],
         REJECTED: ['PENDING', 'CANCELLED'],
         COMPLETED: [], // No further transitions allowed
         CANCELLED: [], // No further transitions allowed
      };

      // Get allowed transitions for current status
      const possibleTransitions = allowedTransitions[currentStatus] || [];

      // If no transition is possible or new status isn't in allowed transitions
      if (!possibleTransitions.includes(newStatus)) {
         return false;
      }

      // Check role-based permissions
      switch (newStatus) {
         case 'APPROVED':
         case 'REJECTED':
            return isLandlord;
         case 'IN_PROGRESS':
         case 'COMPLETED':
            return isHandyman;
         case 'CANCELLED':
            return isLandlord || isHandyman;
         default:
            return true;
      }
   }

   /* ------------------------------------------------------------------------------------------- */

   /**
    * Get all maintenance requests for a tenancy
    */
   async getMaintenanceByTenancy({ request, response, auth }: HttpContext) {
      const user = auth.user!;
      const tenancyId = request.param('tenancyId');
      const page = request.input('page', 1);
      const perPage = request.input('perPage', 15);
      const status = request.input('status', 'ALL'); // Optional status filter

      try {
         // Verify the tenancy exists and user is associated with it
         const tenancy = await Tenancy.findOrFail(tenancyId);

         // Check if user is either the tenant or landlord of this tenancy
         if (tenancy.tenantId !== user.id && tenancy.landlordId !== user.id) {
            return response.forbidden({
               message: 'You are not authorized to view maintenance requests for this tenancy',
            });
         }

         // Build query
         const query = TenancyMaintenance.query().where('tenancyId', tenancyId);

         // Apply status filter if provided
         if (status !== 'ALL') {
            query.where('status', status.toUpperCase());
         }

         const maintenance = await query
            .preload('handyman', (user) => {
               user.preload('profile');
            })
            .orderBy('created_at', 'desc')
            .paginate(page, perPage);

         return response.ok(maintenance);
      }
      catch (error) {
         console.error(error);
         return response.internalServerError({
            message: 'Failed to fetch maintenance requests',
         });
      }
   }

   /* ------------------------------------------------------------------------------------------- */

   /**
    * Get single maintenance request details
    */
   async single({ request, response, auth }: HttpContext) {
      const user = auth.user!;
      const id = request.param('id');

      try {
         const maintenance = await TenancyMaintenance.findOrFail(id);

         // Load the tenancy relationship to check permissions
         await maintenance.load('tenancy');

         // Check if user is authorized to view this maintenance request
         if (
            maintenance.tenancy.tenantId !== user.id
            && maintenance.tenancy.landlordId !== user.id
            && maintenance.handymanId !== user.id
         ) {
            return response.forbidden({
               message: 'You are not authorized to view this maintenance request',
            });
         }

         // Load all necessary relationships
         await maintenance.load('tenancy', (query) => {
            query.preload('tenant', (tenantQuery) => {
               tenantQuery.preload('profile');
            });
            query.preload('landlord', (landlordQuery) => {
               landlordQuery.preload('profile');
            });
            query.preload('property', (propertyQuery) => {
               propertyQuery.preload('propertyType');
               propertyQuery.preload('propertyFeatures');
               propertyQuery.preload('city');
            });
         });

         // Load handyman details if assigned
         if (maintenance.handymanId) {
            await maintenance.load('handyman', (user) => {
               user.preload('profile');
               user.preload('handymanProfile', (profile) => {
                  profile.preload('handymanSkills');
                  profile.preload('cities');
               });
            });
         }

         return response.ok(maintenance);
      }
      catch (error) {
         console.error(error);
         return response.internalServerError({
            message: 'Failed to fetch maintenance request details',
         });
      }
   }

   /* ------------------------------------------------------------------------------------------- */

   /**
    * Landlord approve or reject maintenance request
    */
   async landlordAction({ request, response, auth }: HttpContext) {
      const user = auth.user!;
      const id = request.param('id');
      const payload = await request.validateUsing(vineTenancyMaintenanceLandlordAction);

      try {
         const maintenance = await TenancyMaintenance.findOrFail(id);
         await maintenance.load('tenancy');

         // Verify user is the landlord
         if (maintenance.tenancy.landlordId !== user.id) {
            return response.forbidden({
               message: 'Only the landlord can approve or reject maintenance requests',
            });
         }

         // Verify current status is PENDING
         if (maintenance.status !== 'PENDING') {
            return response.badRequest({
               message: 'Can only approve or reject pending maintenance requests',
            });
         }

         // Update the maintenance request
         maintenance.merge({
            status: payload.action,
            handymanId: payload.handymanId ?? maintenance.handymanId,
            estimatedCost: payload.estimatedCost,
            landlordNotes: payload.landlordNotes,
         });

         await maintenance.save();

         // Reload relationships
         await maintenance.load('tenancy');
         if (maintenance.handymanId) {
            await maintenance.load('handyman');
         }

         return response.ok(maintenance);
      }
      catch (error) {
         console.error(error);
         return response.internalServerError({
            message: 'Failed to process landlord action',
         });
      }
   }

   /* ------------------------------------------------------------------------------------------- */

   /**
    * Update maintenance request status
    */
   async updateStatus({ request, response, auth }: HttpContext) {
      const user = auth.user!;
      const id = request.param('id');
      const payload = await request.validateUsing(vineTenancyMaintenanceStatusUpdate);

      try {
         const maintenance = await TenancyMaintenance.findOrFail(id);
         await maintenance.load('tenancy');

         // Verify user is the assigned handyman
         if (maintenance.handymanId !== user.id) {
            return response.forbidden({
               message: 'Only the assigned handyman can update the maintenance status',
            });
         }

         // Validate status transition
         const isValidTransition = this.validateStatusTransition(
            maintenance.status,
            payload.status,
            false, // not landlord
            true, // is handyman
         );

         if (!isValidTransition) {
            return response.badRequest({
               message: 'Invalid status transition',
            });
         }

         // Update the maintenance request
         maintenance.merge({
            status: payload.status,
            handymanNotes: payload.handymanNotes,
            completionImage1: payload.completionImage1 ?? maintenance.completionImage1,
            completionImage2: payload.completionImage2 ?? maintenance.completionImage2,
            completionImage3: payload.completionImage3 ?? maintenance.completionImage3,
            finalCost: payload.finalCost,
            completionDateTime:
               payload.status === 'COMPLETED' ? DateTime.now() : maintenance.completionDateTime,
         });

         await maintenance.save();

         // Reload relationships
         await maintenance.load('tenancy');
         await maintenance.load('handyman');

         return response.ok(maintenance);
      }
      catch (error) {
         console.error(error);
         return response.internalServerError({
            message: 'Failed to update maintenance status',
         });
      }
   }

   /* ------------------------------------------------------------------------------------------- */

   /**
    * Update landlord notes for maintenance request
    */
   async updateLandlordNotes({ request, response, auth }: HttpContext) {
      const user = auth.user!;
      const id = request.param('id');
      const payload = await request.validateUsing(vineTenancyMaintenanceLandlordNotes);

      try {
         const maintenance = await TenancyMaintenance.findOrFail(id);
         await maintenance.load('tenancy');

         // Verify user is the landlord
         if (maintenance.tenancy.landlordId !== user.id) {
            return response.forbidden({
               message: 'Only the landlord can update notes for this maintenance request',
            });
         }

         // Update the maintenance request
         maintenance.merge({
            landlordNotes: payload.landlordNotes,
         });

         await maintenance.save();

         // Reload relationships
         await maintenance.load('tenancy');
         if (maintenance.handymanId) {
            await maintenance.load('handyman');
         }

         return response.ok(maintenance);
      }
      catch (error) {
         console.error(error);
         return response.internalServerError({
            message: 'Failed to update landlord notes',
         });
      }
   }

   /* ------------------------------------------------------------------------------------------- */

   /**
    * Upload issue image for maintenance request
    */
   async uploadIssueImage({ request, response, auth }: HttpContext) {
      const user = auth.user!;
      const id = request.param('id');
      const imageNumber = request.input('imageNumber');

      // Validate image number
      if (!['1', '2', '3'].includes(imageNumber)) {
         return response.badRequest({
            message: 'Invalid image number. Must be 1, 2, or 3',
         });
      }

      try {
         const maintenance = await TenancyMaintenance.findOrFail(id);
         await maintenance.load('tenancy');

         // Verify user is authorized (tenant or landlord)
         if (
            maintenance.tenancy.tenantId !== user.id
            && maintenance.tenancy.landlordId !== user.id
         ) {
            return response.forbidden({
               message: 'You are not authorized to upload images for this maintenance request',
            });
         }

         const imageFile = request.file('image', {
            size: '5mb',
            extnames: ['jpg', 'jpeg', 'png'],
         });

         if (!imageFile) {
            return response.badRequest({
               message: 'No image file provided',
            });
         }

         if (!imageFile.isValid) {
            return response.badRequest({
               message: 'Invalid file. Please upload a JPG or PNG image under 5MB',
               errors: imageFile.errors,
            });
         }

         // Generate unique key for the image
         const imageKey = `maintenance/issues/${maintenance.id}/${cuid()}.${imageFile.extname}`;

         try {
            // Upload image to storage
            await imageFile.moveToDisk(imageKey);

            // Update the maintenance record with the new image key
            const updateData: Partial<typeof maintenance> = {};
            switch (imageNumber) {
               case '1':
                  updateData.issueImage1 = imageKey;
                  break;
               case '2':
                  updateData.issueImage2 = imageKey;
                  break;
               case '3':
                  updateData.issueImage3 = imageKey;
                  break;
            }

            maintenance.merge(updateData);
            await maintenance.save();

            // Reload relationships
            await maintenance.load('tenancy');
            if (maintenance.handymanId) {
               await maintenance.load('handyman');
            }

            return response.ok({
               message: 'Issue image uploaded successfully',
               maintenance,
            });
         }
         catch (error) {
            console.error('Error uploading issue image:', error);
            return response.internalServerError({
               message: 'Failed to upload issue image',
            });
         }
      }
      catch (error) {
         console.error(error);
         return response.internalServerError({
            message: 'Failed to process issue image upload',
         });
      }
   }

   /* ------------------------------------------------------------------------------------------- */

   /**
    * Upload completion image for maintenance request
    */
   async uploadCompletionImage({ request, response, auth }: HttpContext) {
      const user = auth.user!;
      const id = request.param('id');
      const imageNumber = request.input('imageNumber');

      // Validate image number
      if (!['1', '2', '3'].includes(imageNumber)) {
         return response.badRequest({
            message: 'Invalid image number. Must be 1, 2, or 3',
         });
      }

      try {
         const maintenance = await TenancyMaintenance.findOrFail(id);
         await maintenance.load('tenancy');

         // Verify user is authorized (handyman or tenant)
         if (
            maintenance.handymanId !== user.id
            && maintenance.tenancy.tenantId !== user.id
         ) {
            return response.forbidden({
               message: 'Only the assigned handyman or tenant can upload completion images',
            });
         }

         const imageFile = request.file('image', {
            size: '5mb',
            extnames: ['jpg', 'jpeg', 'png'],
         });

         if (!imageFile) {
            return response.badRequest({
               message: 'No image file provided',
            });
         }

         if (!imageFile.isValid) {
            return response.badRequest({
               message: 'Invalid file. Please upload a JPG or PNG image under 5MB',
               errors: imageFile.errors,
            });
         }

         // Generate unique key for the image
         const imageKey = `maintenance/completion/${maintenance.id}/${cuid()}.${imageFile.extname}`;

         try {
            // Upload image to storage
            await imageFile.moveToDisk(imageKey);

            // Update the maintenance record with the new image key
            const updateData: Partial<typeof maintenance> = {};
            switch (imageNumber) {
               case '1':
                  updateData.completionImage1 = imageKey;
                  break;
               case '2':
                  updateData.completionImage2 = imageKey;
                  break;
               case '3':
                  updateData.completionImage3 = imageKey;
                  break;
            }

            maintenance.merge(updateData);
            await maintenance.save();

            // Reload relationships
            await maintenance.load('tenancy');
            if (maintenance.handymanId) {
               await maintenance.load('handyman');
            }

            return response.ok({
               message: 'Completion image uploaded successfully',
               maintenance,
            });
         }
         catch (error) {
            console.error('Error uploading completion image:', error);
            return response.internalServerError({
               message: 'Failed to upload completion image',
            });
         }
      }
      catch (error) {
         console.error(error);
         return response.internalServerError({
            message: 'Failed to process completion image upload',
         });
      }
   }

   /**
    * Attach handyman to maintenance request
    */
   async attachHandyman({ request, response, auth }: HttpContext) {
      const user = auth.user!;
      const maintenanceId = request.param('id');
      const handymanId = request.param('handymanId');

      try {
         // Find the maintenance request
         const maintenance = await TenancyMaintenance.findOrFail(maintenanceId);
         await maintenance.load('tenancy');

         // Verify user is either the landlord or tenant
         if (maintenance.tenancy.landlordId !== user.id
            && maintenance.tenancy.tenantId !== user.id) {
            return response.forbidden({
               message: 'Only the landlord or tenant can assign handymen to maintenance requests',
            });
         }

         // Verify maintenance request is in a valid state
         if (!['PENDING', 'APPROVED'].includes(maintenance.status)) {
            return response.badRequest({
               message: 'Can only assign handyman to pending or approved maintenance requests',
            });
         }

         // Check if handyman is already assigned
         if (maintenance.handymanId) {
            return response.badRequest({
               message: 'A handyman is already assigned to this maintenance request',
            });
         }

         // Get handyman profile using handyman ID
         const handymanProfile = await HandymanProfile.query()
            .where('user_id', handymanId)
            .andWhere('adminApproved', true) // Ensure handyman is approved
            .preload('user', (query) => {
               query.preload('profile');
            })
            .firstOrFail();

         // Load landlord information
         await maintenance.load('tenancy', (query) => {
            query.preload('landlord', (landlordQuery) => {
               landlordQuery.preload('profile');
            });
         });

         // Update the maintenance request
         maintenance.merge({
            handymanId,
            handymanProfileId: handymanProfile.id,
            handymanStatus: 'PENDING',
         });

         await maintenance.save();

         // Send SMS to handyman
         if (handymanProfile.user.profile.phone1) {
            await SmsGatewayResource.sendMessage(
               handymanProfile.user.profile.phone1,
               `You have a new maintenance request: ${maintenance.title} . Please login to accept or reject the job.`,
            );
         }

         // Send SMS to landlord
         if (maintenance.tenancy.landlord.profile.phone1) {
            await SmsGatewayResource.sendMessage(
               maintenance.tenancy.landlord.profile.phone1,
               `Handyman assigned for the maintenance request. Handyman ${handymanProfile.user.profile.fullName}. maintenance request: ${maintenance.title}. Please login to view more details.`,
            );
         }

         // Reload relationships for response
         await maintenance.load('tenancy');
         await maintenance.load('handymanProfile', (query) => {
            query.preload('user', (userQuery) => {
               userQuery.preload('profile');
            });
         });

         return response.ok(maintenance);
      }
      catch (error) {
         console.error(error);
         if (error.code === 'E_ROW_NOT_FOUND') {
            return response.notFound({
               message: 'Handyman not found or not approved',
            });
         }
         return response.internalServerError({
            message: 'Failed to attach handyman to maintenance request',
         });
      }
   }

   /**
    * Get all maintenance requests for a handyman
    */
   async getMaintenanceByHandyman({ request, response, auth }: HttpContext) {
      const user = auth.user!;
      const handymanId = request.param('handymanId');
      const page = request.input('page', 1);
      const perPage = request.input('perPage', 15);
      const status = request.input('status', 'ALL');
      const handymanStatus = request.input('handymanStatus', 'ALL');

      // Verify the requesting user is the handyman
      if (user.id !== Number(handymanId)) {
         return response.forbidden({
            message: 'You can only view your own maintenance requests',
         });
      }

      try {
         // Build query
         const query = TenancyMaintenance.query()
            .where('handymanId', handymanId);

         // Apply maintenance status filter if provided
         if (status !== 'ALL') {
            query.where('status', status.toUpperCase());
         }

         // Apply handyman status filter if provided
         if (handymanStatus !== 'ALL') {
            query.where('handymanStatus', handymanStatus.toUpperCase());
         }

         // Load relationships and paginate results
         const maintenance = await query
            .preload('handyman', (handymanQuery) => {
               handymanQuery.preload('profile');
               handymanQuery.preload('handymanProfile', (profile) => {
                  profile.preload('handymanSkills');
                  profile.preload('cities');
               });
            })
            .preload('tenancy', (tenancyQuery) => {
               tenancyQuery.preload('tenant', (tenantQuery) => {
                  tenantQuery.preload('profile');
               });
               tenancyQuery.preload('landlord', (landlordQuery) => {
                  landlordQuery.preload('profile');
               });
               tenancyQuery.preload('property', (propertyQuery) => {
                  propertyQuery.preload('propertyType');
                  propertyQuery.preload('propertyFeatures');
                  propertyQuery.preload('city');
               });
            })
            .orderBy('created_at', 'desc')
            .paginate(page, perPage);

         return response.ok(maintenance);
      }
      catch (error) {
         console.error(error);
         return response.internalServerError({
            message: 'Failed to fetch maintenance requests',
         });
      }
   }

   /**
    * Handyman accept or reject maintenance request
    */
   async handymanAction({ request, response, auth }: HttpContext) {
      const user = auth.user!;
      const id = request.param('id');
      const payload = await request.validateUsing(vineTenancyMaintenanceHandymanAction);

      try {
         const maintenance = await TenancyMaintenance.findOrFail(id);
         await maintenance.load('tenancy');

         // Verify user is the assigned handyman
         if (maintenance.handymanId !== user.id) {
            return response.forbidden({
               message: 'Only the assigned handyman can update this maintenance request',
            });
         }

         // Validate status transition
         const isValidTransition = this.validateHandymanStatusTransition(
            maintenance.handymanStatus,
            payload.status,
         );

         if (!isValidTransition) {
            return response.badRequest({
               message: `Invalid status transition from ${maintenance.handymanStatus} to ${payload.status}`,
            });
         }

         // Prepare update data
         const updateData: Partial<typeof maintenance> = {
            handymanStatus: payload.status,
            handymanNotes: payload.notes ?? maintenance.handymanNotes,
         };

         // Add acceptance-specific data
         if (payload.status === 'ACCEPTED') {
            if (!payload.estimatedCost) {
               return response.badRequest({
                  message: 'Estimated cost is required when accepting a maintenance request',
               });
            }
            updateData.estimatedCost = payload.estimatedCost;
         }

         // Add completion-specific data
         if (payload.status === 'COMPLETED') {
            if (!payload.finalCost) {
               return response.badRequest({
                  message: 'Final cost is required when completing a maintenance request',
               });
            }
            updateData.completionDateTime = DateTime.now();
            updateData.finalCost = payload.finalCost;
         }

         // Clear handyman assignments if rejected
         if (payload.status === 'REJECTED') {
            updateData.handymanId = null;
            updateData.handymanProfileId = null;
            updateData.estimatedCost = null;
         }

         // Update the maintenance request
         maintenance.merge(updateData);
         await maintenance.save();

         // Reload relationships
         await maintenance.load('tenancy');
         await maintenance.load('handyman', (handymanQuery) => {
            handymanQuery.preload('profile');
            handymanQuery.preload('handymanProfile', (profile) => {
               profile.preload('handymanSkills');
               profile.preload('cities');
            });
         });

         return response.ok(maintenance);
      }
      catch (error) {
         console.error(error);
         return response.internalServerError({
            message: 'Failed to process handyman action',
         });
      }
   }

   /**
    * Validate handyman status transitions
    */
   private validateHandymanStatusTransition(
      currentStatus: string | null,
      newStatus: string,
   ): boolean {
      const allowedTransitions: Record<string, string[]> = {
         PENDING: ['ACCEPTED', 'REJECTED'],
         ACCEPTED: ['STARTED', 'REJECTED'],
         STARTED: ['COMPLETED', 'REJECTED'],
         COMPLETED: [], // No further transitions allowed
         REJECTED: [], // No further transitions allowed
         Expired: [], // No further transitions allowed
      };

      if (!currentStatus) {
         return ['ACCEPTED', 'REJECTED'].includes(newStatus);
      }

      return allowedTransitions[currentStatus]?.includes(newStatus) ?? false;
   }
}
