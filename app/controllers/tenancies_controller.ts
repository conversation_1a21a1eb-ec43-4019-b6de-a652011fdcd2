import type { HttpContext } from '@adonisjs/core/http';
import { DateTime } from 'luxon';
import { UserResource } from '../resources/user_resource.js';
import { vineTenancyCreate, vineUpdateTenancyStatus } from '#validators/tenancy';
import Tenancy from '#models/tenancy';
import PropertyVisit from '#models/property_visit';
import PropertyListing from '#models/property_listing';

/* ---------------------------------------------------------------------------------------------- */

export default class TenanciesController {
   /**
    * Create a new tenancy agreement
    */
   async create({ request, response, auth }: HttpContext) {
      const user = auth.user!;

      // Validate incoming request
      const payload = await request.validateUsing(vineTenancyCreate);

      // Verify that landlord is actually a landlord
      if (!UserResource.onlyLandlord(user)) {
         return response.forbidden({
            message: 'Only landlords can create tenancy agreements',
         });
      }

      try {
         // Check for existing active tenancy for the property
         const existingTenancy = await Tenancy.query()
            .where('propertyId', payload.propertyId)
            .andWhereIn('status', ['ACTIVE', 'PENDING', 'RENEWED'])
            .first();

         if (existingTenancy) {
            return response.conflict({
               message: 'An active or pending tenancy already exists for this property',
            });
         }

         // Convert string dates to DateTime objects
         const tenancy = await Tenancy.create({
            ...payload,
            tenancyStart: DateTime.fromISO(payload.tenancyStart),
            rentDueDay: payload.rentDueDay,
            agreementDate: DateTime.fromISO(payload.agreementDate),
            terminationDate: payload.terminationDate ? DateTime.fromISO(payload.terminationDate) : undefined,
            actualEndDate: payload.actualEndDate ? DateTime.fromISO(payload.actualEndDate) : undefined,
            status: payload.status ?? 'PENDING',
         });

         const visit = await PropertyVisit.find(payload.visitId);

         /*
          * Update visit status and cancel other visits for the same listing
          */
         if (visit) {
            visit.status = 'TENANCY_CREATED';
            visit.archived = true;
            await visit.save();

            await PropertyVisit.query()
               .where('listingId', visit.listingId)
               .andWhere('id', '!=', visit.id)
               .andWhereIn('status', ['PENDING', 'APPROVED'])
               .update({
                  status: 'CANCELLED_BY_LANDLORD',
                  archived: true,
               });
         }

         /*
          * Deactivate property listings for the property
          */
         await PropertyListing.query()
            .where('propertyId', payload.propertyId)
            .update({
               status: 'INACTIVE',
            });

         // Load relationships
         await tenancy.load('tenant');
         await tenancy.load('landlord');

         return response.created(tenancy);
      }
      catch (error) {
         console.error(error);
         return response.internalServerError({
            message: 'Failed to create tenancy agreement',
         });
      }
   }

   /* ------------------------------------------------------------------------------------------- */

   /**
    * Get all tenancies for a landlord with optional active/completed filtering
    */
   async getAllTenanciesByLandlord({ request, response }: HttpContext) {
      const landlordId = request.param('landlordId');
      const page = request.input('page', 1) || 1;
      const perPage = request.input('perPage', 15) || 15;
      const status = request.input('status', 'ALL'); // 'ACTIVE', 'COMPLETED', 'EARLY_TERMINATED', 'PENDING', 'RENEWED', 'ALL'

      if (!landlordId) {
         return response.badRequest({ message: 'LandlordId is required' });
      }

      try {
         const query = Tenancy.query()
            .where('landlordId', landlordId);

         // const now = DateTime.now().setZone('UTC');

         if (status !== 'ALL') {
            if (status === 'ACTIVE') {
               query.andWhere('status', 'ACTIVE');
            }
            else {
               query.andWhere('status', status.toUpperCase());
            }
         }

         const tenancies = await query
            .preload('tenant', (tenant) => {
               tenant.preload('profile');
            })
            .preload('landlord', (landlord) => {
               landlord.preload('profile');
            })
            .orderBy('tenancyStart', 'desc')
            .paginate(page, perPage);

         return response.ok(tenancies);
      }
      catch (error) {
         console.error(error);
         return response.internalServerError({
            message: 'Failed to fetch tenancies',
         });
      }
   }

   /* ------------------------------------------------------------------------------------------- */

   async getAllTenanciesByTenant({ request, response }: HttpContext) {
      const tenantId = request.param('tenantId');
      const page = request.input('page', 1);
      const perPage = request.input('perPage', 15);
      const status = request.input('status', 'ALL'); // 'ACTIVE', 'COMPLETED', 'EARLY_TERMINATED', 'PENDING', 'RENEWED', 'ALL'

      if (!tenantId) {
         return response.badRequest({ message: 'TenantId is required' });
      }

      try {
         const query = Tenancy.query()
            .where('tenantId', tenantId);

         const now = DateTime.now().setZone('UTC');

         if (status !== 'ALL') {
            if (status === 'ACTIVE') {
               query.where('status', 'ACTIVE')
                  .whereRaw('DATE_ADD(tenancy_start, INTERVAL tenancy_duration MONTH) > ?', [now.toFormat('yyyy-MM-dd HH:mm:ss')]);
            }
            else {
               query.where('status', status.toUpperCase());
            }
         }

         const tenancies = await query
            .preload('tenant', (tenant) => {
               tenant.preload('profile');
            })
            .preload('landlord', (landlord) => {
               landlord.preload('profile');
            })
            .orderBy('tenancyStart', 'desc')
            .paginate(page, perPage);

         return response.ok(tenancies);
      }
      catch (error) {
         console.error(error);
         return response.internalServerError({
            message: 'Failed to fetch tenancies',
         });
      }
   }

   /* ------------------------------------------------------------------------------------------- */

   /**
    * Get single tenancy details
    */
   async single({ request, response }: HttpContext) {
      const id = request.param('id');

      if (!id) {
         return response.badRequest({ message: 'Tenancy ID is required' });
      }

      try {
         const tenancy = await Tenancy.query()
            .where('id', id)
            .preload('tenant', (tenant) => {
               tenant.preload('profile');
            })
            .preload('landlord', (landlord) => {
               landlord.preload('profile');
            })
            .preload('property', (property) => {
               property.preload('propertyType');
               property.preload('propertyFeatures');
               property.preload('propertyImages');
               property.preload('city');
            })
            .first();

         if (!tenancy) {
            return response.notFound({ message: 'Tenancy not found' });
         }

         return response.ok(tenancy);
      }
      catch (error) {
         console.error(error);
         return response.internalServerError({
            message: 'Failed to fetch tenancy details',
         });
      }
   }

   /* ------------------------------------------------------------------------------------------- */

   /**
    * Update tenancy status
    */
   async updateStatus({ request, response, auth }: HttpContext) {
      const user = auth.user!;
      const id = request.param('id');

      if (!id) {
         return response.badRequest({ message: 'Tenancy ID is required' });
      }

      try {
         const tenancy = await Tenancy.query()
            .where('id', id)
            .first();

         if (!tenancy) {
            return response.notFound({ message: 'Tenancy not found' });
         }

         // Allow both landlord and tenant to update the status
         if (tenancy.landlordId !== user.id && tenancy.tenantId !== user.id) {
            return response.forbidden({
               message: 'Only the landlord or tenant can update tenancy status',
            });
         }

         const payload = await request.validateUsing(vineUpdateTenancyStatus);

         tenancy.status = payload.status;

         if (payload.status === 'EARLY_TERMINATED') {
            if (!payload.terminationReason) {
               return response.badRequest({
                  message: 'Termination reason is required for early termination',
               });
            }
            tenancy.terminationReason = payload.terminationReason;
            tenancy.terminationDate = payload.terminationDate
               ? DateTime.fromISO(payload.terminationDate)
               : DateTime.now();
         }

         if (payload.status === 'COMPLETED' || payload.status === 'EARLY_TERMINATED') {
            tenancy.actualEndDate = payload.actualEndDate
               ? DateTime.fromISO(payload.actualEndDate)
               : DateTime.now();
         }

         await tenancy.save();

         // Reload relationships
         await tenancy.load('tenant', (tenant) => {
            tenant.preload('profile');
         });
         await tenancy.load('landlord', (landlord) => {
            landlord.preload('profile');
         });
         await tenancy.load('property', (property) => {
            property.preload('propertyType');
            property.preload('propertyFeatures');
            property.preload('propertyImages');
            property.preload('city');
         });

         return response.ok(tenancy);
      }
      catch (error) {
         console.error(error);
         return response.internalServerError({
            message: 'Failed to update tenancy status',
         });
      }
   }

   /* ------------------------------------------------------------------------------------------- */

   /**
    * Delete a tenancy
    */
   async delete({ request, response, auth }: HttpContext) {
      const user = auth.user!;
      const id = request.param('id');

      if (!id) {
         return response.badRequest({ message: 'Tenancy ID is required' });
      }

      try {
         const tenancy = await Tenancy.query()
            .where('id', id)
            .first();

         if (!tenancy) {
            return response.notFound({ message: 'Tenancy not found' });
         }

         // Only landlord can delete the tenancy
         if (tenancy.landlordId !== user.id) {
            return response.forbidden({
               message: 'Only the landlord can delete tenancy',
            });
         }

         // Can only delete if status is PENDING or REJECTED
         if (!['PENDING', 'REJECTED'].includes(tenancy.status)) {
            return response.forbidden({
               message: 'Can only delete pending or rejected tenancies',
            });
         }

         // If there's an associated visit, update its status
         if (tenancy.visitId) {
            const visit = await PropertyVisit.find(tenancy.visitId);
            if (visit) {
               visit.status = 'CANCELLED_BY_LANDLORD';
               visit.archived = true;
               await visit.save();
            }
         }

         await tenancy.delete();

         return response.ok({
            message: 'Tenancy deleted successfully',
         });
      }
      catch (error) {
         console.error(error);
         return response.internalServerError({
            message: 'Failed to delete tenancy',
         });
      }
   }

   async uploadSignedAgreement({ request, response }: HttpContext) {
      const tenancyId = request.param('id');
      const agreementFile = request.file('agreement');

      if (!agreementFile) {
         return response.badRequest({ message: 'No file uploaded' });
      }

      try {
         const tenancy = await Tenancy.findOrFail(tenancyId);

         const key = `tenancies/${tenancy.id}/agreements/agreement_${Date.now()}.${agreementFile.extname}`;

         await agreementFile.moveToDisk(key);

         tenancy.signedAgreementUrlKey = key;
         await tenancy.save();

         return response.ok({
            message: 'Agreement uploaded successfully',
            signedAgreementUrl: tenancy.signedAgreementUrl,
         });
      }
      catch (error) {
         console.error('Error uploading agreement:', error);
         return response.internalServerError({
            message: 'Failed to upload agreement',
         });
      }
   }
}
