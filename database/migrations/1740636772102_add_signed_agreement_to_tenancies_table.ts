import { BaseSchema } from '@adonisjs/lucid/schema';

export default class extends BaseSchema {
   protected tableName = 'tenancies';

   async up() {
      this.schema.alterTable(this.tableName, (table) => {
         table.string('signed_agreement_url_key').nullable();
      });
   }

   async down() {
      this.schema.alterTable(this.tableName, (table) => {
         table.dropColumn('signed_agreement_url_key');
      });
   }
}
