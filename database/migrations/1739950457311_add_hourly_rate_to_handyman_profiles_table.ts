import { BaseSchema } from '@adonisjs/lucid/schema';

export default class extends BaseSchema {
   protected tableName = 'handyman_profiles';

   async up() {
      this.schema.alterTable(this.tableName, (table) => {
         table.integer('hourly_rate').nullable();
      });
   }

   async down() {
      this.schema.alterTable(this.tableName, (table) => {
         table.dropColumn('hourly_rate');
      });
   }
}
