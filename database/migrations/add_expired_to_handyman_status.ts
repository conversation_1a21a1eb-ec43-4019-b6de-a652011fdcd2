import { BaseSchema } from '@adonisjs/lucid/schema';

export default class extends BaseSchema {
   protected tableName = 'tenancy_maintenance';

   async up() {
      this.schema.alterTable(this.tableName, (table) => {
         table.enum('handyman_status', [
            'PENDING',
            'ACCEPTED',
            'REJECTED',
            'STARTED',
            'COMPLETED',
            'EXPIRED',
         ]).nullable().alter();
      });
   }

   async down() {
      this.schema.alterTable(this.tableName, (table) => {
         table.enum('handyman_status', [
            'PENDING',
            'ACCEPTED',
            'REJECTED',
            'STARTED',
            'COMPLETED',
         ]).nullable().alter();
      });
   }
}
